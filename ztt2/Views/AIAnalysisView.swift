//
//  AIAnalysisView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI分析主界面
 * 参考ztt1的设计，根据权限状态显示不同的界面
 */
struct AIAnalysisView: View {

    // MARK: - Properties

    let member: Member
    @StateObject private var viewModel = AIAnalysisViewModel()
    @Environment(\.dismiss) private var dismiss
    @State private var pageAppeared = false
    @State private var selectedReportType: AIReportType = .behaviorAnalysis

    // MARK: - Body

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                createBackgroundGradient()

                // 主要内容
                if viewModel.canGenerateAnyReport(for: member) {
                    if let report = viewModel.currentReport {
                        // 显示报告内容
                        reportContentView(report: report)
                    } else if viewModel.isGenerating {
                        // 加载状态
                        loadingView
                    } else {
                        // 生成报告界面
                        generateReportView
                    }
                } else {
                    // 权限不足视图
                    permissionDeniedView
                }

                // 顶部导航栏
                topNavigationBar
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupView()
        }
        .alert("数据不足", isPresented: .constant(viewModel.errorMessage?.contains("记录") == true)) {
            Button("确定") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("网络错误", isPresented: .constant(viewModel.errorMessage?.contains("网络") == true)) {
            Button("重试") {
                Task {
                    await generateSelectedReport()
                }
            }
            Button("取消", role: .cancel) {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }

    // MARK: - View Components

    /**
     * 设置视图
     */
    private func setupView() {
        viewModel.setupMember(member)

        // 延迟显示动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation {
                pageAppeared = true
            }
        }
    }

    /**
     * 生成选中的报告
     */
    private func generateSelectedReport() async {
        switch selectedReportType {
        case .behaviorAnalysis:
            await viewModel.generateBehaviorAnalysisReport(for: member)
        case .growthReport:
            await viewModel.generateGrowthReport(for: member)
        }
    }

    /**
     * 创建背景渐变
     */
    private func createBackgroundGradient() -> some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(.systemBackground),
                Color(.systemGray6).opacity(0.3)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea()
    }

    /**
     * 顶部导航栏
     */
    private var topNavigationBar: some View {
        VStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                }

                Spacer()

                Text("AI分析报告")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()

                // 占位符保持居中
                Color.clear
                    .frame(width: 18, height: 18)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)

            Spacer()
        }
    }

    /**
     * 权限不足视图
     */
    private var permissionDeniedView: some View {
        VStack(spacing: 24) {
            Spacer()

            // 权限图标
            Image(systemName: "lock.circle")
                .font(.system(size: 64))
                .foregroundColor(Color.orange)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .scaleEffect(pageAppeared ? 1.0 : 0.8)
                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: pageAppeared)

            // 权限说明
            VStack(spacing: 12) {
                Text("需要高级会员")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.primary)

                Text(viewModel.getPermissionStatusDescription(for: member))
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            .opacity(pageAppeared ? 1.0 : 0.0)
            .offset(y: pageAppeared ? 0 : 20)
            .animation(.easeOut(duration: 0.6).delay(0.4), value: pageAppeared)

            // 升级按钮
            if viewModel.canShowUpgradeButton(for: member) {
                Button(action: {
                    // 关闭当前页面并跳转到订阅页面
                    dismiss()
                    // TODO: 触发订阅页面跳转
                }) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 16))
                        Text("升级会员")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.orange,
                                Color.orange.opacity(0.8)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: Color.orange.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 20)
                .animation(.easeOut(duration: 0.6).delay(0.6), value: pageAppeared)
            }

            Spacer()
        }
        .padding(.horizontal, 24)
    }

    /**
     * 生成报告视图
     */
    private var generateReportView: some View {
        VStack(spacing: 24) {
            Spacer()

            // 成员信息卡片
            memberInfoCard

            // 报告类型选择器
            reportTypeSelector
                .padding(.top, 8)

            // 生成按钮
            Button(action: {
                Task {
                    await generateSelectedReport()
                }
            }) {
                HStack {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 18))
                    Text("生成分析报告")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.green,
                            Color.green.opacity(0.8)
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(25)
                .shadow(color: Color.green.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(!viewModel.isNetworkAvailable)
            .opacity(viewModel.isNetworkAvailable ? 1.0 : 0.6)

            // 网络状态提示
            if !viewModel.isNetworkAvailable {
                Text("网络连接不可用")
                    .font(.system(size: 14))
                    .foregroundColor(.red)
            }

            Spacer()
        }
        .padding(.horizontal, 24)
        .opacity(pageAppeared ? 1.0 : 0.0)
        .offset(y: pageAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: pageAppeared)
    }

    /// 成员信息卡片
    private var memberInfoCard: some View {
        VStack(spacing: 16) {
            // 头像和基本信息
            HStack(spacing: 16) {
                Image(member.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 50, height: 50)
                    .clipShape(Circle())

                VStack(alignment: .leading, spacing: 4) {
                    Text(member.displayName)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)

                    Text("\(member.roleDisplayName) · \(member.age)岁")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // 数据统计
            HStack(spacing: 20) {
                dataStatItem(
                    title: "行为记录",
                    count: member.sortedPointRecords.filter { !$0.isReversed }.count,
                    minRequired: 10
                )

                Divider()
                    .frame(height: 30)

                dataStatItem(
                    title: "成长日记",
                    count: member.sortedGrowthDiaries.count,
                    minRequired: 10
                )
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }

    /**
     * 报告类型选择器
     */
    private var reportTypeSelector: some View {
        VStack(spacing: 12) {
            Text("选择报告类型")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 两种报告类型的选择按钮
            HStack(spacing: 12) {
                reportTypeButton(
                    type: .behaviorAnalysis,
                    icon: "chart.line.uptrend.xyaxis",
                    title: "学生分析报告",
                    description: "专业分析报告"
                )

                reportTypeButton(
                    type: .growthReport,
                    icon: "message",
                    title: "家长反馈",
                    description: "与家长沟通"
                )
            }
        }
    }

    /**
     * 报告类型按钮
     */
    private func reportTypeButton(type: AIReportType, icon: String, title: String, description: String) -> some View {
        let isSelected = selectedReportType == type

        return Button(action: {
            selectedReportType = type
        }) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: icon)
                        .font(.system(size: 16))
                        .foregroundColor(isSelected ? Color.white : Color.green)

                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(isSelected ? Color.white : .primary)
                }

                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(isSelected ? Color.white.opacity(0.9) : .secondary)
                    .lineLimit(2)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                isSelected ?
                LinearGradient(
                    gradient: Gradient(colors: [Color.green, Color.green.opacity(0.8)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ) :
                LinearGradient(
                    gradient: Gradient(colors: [Color(.systemGray6), Color(.systemGray6)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.green : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 数据统计项
    private func dataStatItem(title: String, count: Int, minRequired: Int) -> some View {
        VStack(spacing: 4) {
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(count >= minRequired ? .primary : .secondary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            if count < minRequired {
                Text("需要\(minRequired)条")
                    .font(.caption2)
                    .foregroundColor(.red)
            }
        }
        .frame(maxWidth: .infinity)
    }

    /**
     * 加载视图
     */
    private var loadingView: some View {
        VStack(spacing: 24) {
            Spacer()

            // 加载动画
            ProgressView()
                .scaleEffect(1.5)
                .tint(Color.green)

            Text("AI正在分析中...")
                .font(.system(size: 16))
                .foregroundColor(.secondary)

            Text("请稍候，这可能需要10-30秒")
                .font(.system(size: 14))
                .foregroundColor(.secondary)

            Spacer()
        }
    }

    /**
     * 报告内容视图
     */
    private func reportContentView(report: AIAnalysisReport) -> some View {
        VStack(spacing: 0) {
            // 报告详细内容
            AIReportDetailView(report: report)
        }
        .opacity(pageAppeared ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.6), value: pageAppeared)
    }

}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let shortDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}
