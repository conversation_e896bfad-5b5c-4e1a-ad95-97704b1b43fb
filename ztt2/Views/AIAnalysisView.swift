//
//  AIAnalysisView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI分析主界面
 * 提供行为分析和成长报告的生成入口
 */
struct AIAnalysisView: View {
    
    // MARK: - Properties
    
    let member: Member
    @StateObject private var viewModel = AIAnalysisViewModel()
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 成员信息卡片
                    memberInfoCard
                    
                    // AI分析功能卡片
                    analysisOptionsCard
                    
                    // 当前生成的报告
                    if let report = viewModel.currentReport {
                        currentReportCard(report)
                    }
                    
                    // 历史报告入口
                    historyReportsCard
                }
                .padding()
            }
            .navigationTitle("AI分析")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("完成") {
                        dismiss()
                    }
                }
            }
        }
        .alert("权限不足", isPresented: $viewModel.showingUpgradePrompt) {
            But<PERSON>("升级会员") {
                // TODO: 跳转到订阅页面
            }
            But<PERSON>("取消", role: .cancel) { }
        } message: {
            Text("AI分析功能需要高级会员权限，是否立即升级？")
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .sheet(isPresented: $viewModel.showingHistoryReports) {
            AIAnalysisHistoryView(member: member)
        }
        .onAppear {
            viewModel.loadHistoryReports(for: member)
        }
    }
    
    // MARK: - View Components
    
    /// 成员信息卡片
    private var memberInfoCard: some View {
        VStack(spacing: 12) {
            // 头像和基本信息
            HStack(spacing: 16) {
                Image(member.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 60, height: 60)
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(member.displayName)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("\(member.roleDisplayName) · \(member.age)岁")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    if !viewModel.isNetworkAvailable {
                        Label("网络不可用", systemImage: "wifi.slash")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                }
                
                Spacer()
            }
            
            // 数据统计
            HStack(spacing: 20) {
                dataStatItem(
                    title: "行为记录",
                    count: member.sortedPointRecords.count,
                    minRequired: 10
                )
                
                Divider()
                    .frame(height: 30)
                
                dataStatItem(
                    title: "成长日记",
                    count: member.sortedGrowthDiaries.count,
                    minRequired: 10
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    /// 数据统计项
    private func dataStatItem(title: String, count: Int, minRequired: Int) -> some View {
        VStack(spacing: 4) {
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(count >= minRequired ? .primary : .secondary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            if count < minRequired {
                Text("需要\(minRequired)条")
                    .font(.caption2)
                    .foregroundColor(.red)
            }
        }
        .frame(maxWidth: .infinity)
    }
    
    /// AI分析选项卡片
    private var analysisOptionsCard: some View {
        VStack(spacing: 16) {
            Text("AI分析功能")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                // 行为分析报告
                analysisOptionButton(
                    title: "生成行为分析报告",
                    subtitle: "基于积分记录分析行为趋势",
                    icon: "chart.line.uptrend.xyaxis",
                    reportType: .behaviorAnalysis
                )
                
                // 成长报告
                analysisOptionButton(
                    title: "生成成长报告",
                    subtitle: "基于成长日记分析发展状况",
                    icon: "heart.text.square",
                    reportType: .growthReport
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    /// AI分析选项按钮
    private func analysisOptionButton(
        title: String,
        subtitle: String,
        icon: String,
        reportType: AIReportType
    ) -> some View {
        Button {
            Task {
                switch reportType {
                case .behaviorAnalysis:
                    await viewModel.generateBehaviorAnalysisReport(for: member)
                case .growthReport:
                    await viewModel.generateGrowthReport(for: member)
                }
            }
        } label: {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.blue)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(viewModel.getPermissionDescription(for: member, reportType: reportType))
                        .font(.caption2)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                if viewModel.isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
        .disabled(viewModel.isGenerating || !viewModel.canGenerateReport(for: member, reportType: reportType).canGenerate)
    }
    
    /// 当前报告卡片
    private func currentReportCard(_ report: AIAnalysisReport) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("最新生成的报告")
                    .font(.headline)
                
                Spacer()
                
                Text(DateFormatter.shortDateTime.string(from: report.createdAt))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            NavigationLink {
                AIReportDetailView(report: report)
            } label: {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(report.reportType.displayName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        
                        Text("点击查看详细内容")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "doc.text")
                        .foregroundColor(.blue)
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    /// 历史报告卡片
    private var historyReportsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("历史报告")
                .font(.headline)
            
            Button {
                viewModel.showingHistoryReports = true
            } label: {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("查看所有历史报告")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        
                        Text("共\(viewModel.historyReports.count)份报告")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "folder")
                        .foregroundColor(.orange)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let shortDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}
