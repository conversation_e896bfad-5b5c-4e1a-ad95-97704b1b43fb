# AI分析功能实现方案

## 项目概述
转团团应用的AI分析功能，为高级会员提供基于积分记录和成长日记的智能分析服务，生成个性化的教育建议和成长报告。

## 功能架构

### 1. 核心功能模块
- **分析报告生成**：基于积分记录（加/扣分）进行行为分析
- **成长报告生成**：基于成长日记进行深度教育分析
- **报告存储管理**：Markdown格式报告的本地存储和历史记录
- **使用限制控制**：每日使用次数限制机制

### 2. 技术栈
- **AI服务**：DeepSeek R1 API
- **数据存储**：CoreData + CloudKit同步
- **UI框架**：SwiftUI
- **网络检测**：Network框架
- **报告渲染**：Markdown渲染组件

## 数据流程设计

### 输入数据处理
1. **数据脱敏**：
   - 姓名 → 角色名称（"儿子"/"女儿"）
   - 生日 → 年龄计算（如"7岁"）
   - 保留时间戳用于时间线分析

2. **数据验证**：
   - 积分记录 ≥ 10条（分析报告）
   - 日记记录 ≥ 10条（成长报告）
   - 高级会员权限验证
   - 网络连接状态检查

### 输出数据处理
1. **报告格式**：Markdown格式
2. **存储分类**：分析报告 / 成长报告
3. **历史记录**：按时间排序，支持查看历史生成记录

## 使用限制机制

### 每日限制规则
- 每个成员每天最多生成2次分析报告
- 每个成员每天最多生成2次成长报告
- 限制数据存储在CoreData中，利用CloudKit同步

### 实现方式
```swift
// 示例数据结构
struct AIAnalysisUsage {
    let memberID: String
    let date: Date
    let analysisReportCount: Int
    let growthReportCount: Int
}
```

## API集成设计

### DeepSeek R1 API调用
- **端点**：DeepSeek R1 Chat Completion API
- **认证**：API Key认证
- **请求格式**：JSON格式，包含system prompt和user prompt
- **响应处理**：解析Markdown格式内容

### 错误处理
- 网络连接失败
- API调用超时
- API配额超限
- 数据格式错误

## Prompt模板设计

### 生成分析报告Prompt模板

```
你是一位专业的儿童行为分析师，请根据以下信息为家长生成一份详细的行为分析报告：

**基本信息：**
- 角色：{role} (儿子/女儿)
- 年龄：{age}岁

**积分记录数据：**
{score_records}
格式示例：
- 2024-01-15 10:30 +5分 主动帮忙洗碗
- 2024-01-14 16:20 -3分 不按时完成作业
- 2024-01-13 09:15 +2分 早起不赖床

请生成包含以下内容的分析报告（使用Markdown格式）：

## 行为趋势分析
分析最近的行为变化趋势，识别积极和消极的行为模式。

## 行为模式识别
识别孩子的行为规律，如特定时间段的行为特点。

## 进步亮点总结
突出孩子近期的积极变化和值得表扬的地方。

## 注意事项提醒
基于扣分记录，提醒家长需要关注的行为问题。

## 个性化教育建议
结合近期表现，提供具体的教育指导建议。

## 激励建议
针对孩子当前表现，建议合适的奖励和激励方式。

## 推荐加/扣分规则
基于分析结果，推荐3条新的加分或扣分规则：
1. [规则名称] +/-[分数]分 - [使用场景说明]
2. [规则名称] +/-[分数]分 - [使用场景说明]  
3. [规则名称] +/-[分数]分 - [使用场景说明]

请确保分析客观、建议实用，语言温和且具有指导性。
```

### 生成成长报告Prompt模板

```
你是一位资深的家庭教育专家，拥有丰富的儿童心理学和教育学背景。请以专业而温暖的语调，为家长生成一份深度的成长分析报告：

**基本信息：**
- 角色：{role} (儿子/女儿)
- 年龄：{age}岁

**成长日记内容：**
{diary_records}
格式示例：
- 2024-01-15 今天孩子在学校和同学发生了争吵，回家后情绪低落
- 2024-01-14 孩子主动帮我做家务，还说想学做饭
- 2024-01-13 孩子对新的数学题很感兴趣，一直在研究

请以家庭教育专家的视角，生成包含以下内容的成长报告（使用Markdown格式）：

## 情绪变化分析
深入分析孩子的情绪状态变化，识别情绪模式和可能的触发因素。

## 成长观察与洞察
从专业角度观察孩子的成长表现，发现成长亮点和需要关注的方面。

## 亲子沟通建议
基于日记内容，提供具体的沟通策略和技巧，帮助家长更好地理解和回应孩子。

## 家庭教育建议
提供专业的教育指导，包括：
- 针对当前阶段的教育重点
- 具体的教育方法和策略
- 家庭环境营造建议
- 日常互动改善建议

## 推荐书籍
根据孩子的年龄和日记中反映的特点，推荐3-5本适合的书籍：
- 《书名》 - 推荐理由和如何使用
- 《书名》 - 推荐理由和如何使用

## 专家提醒
作为家庭教育专家，我想特别提醒您关注的几个方面：
[基于日记内容，提供专业的提醒和建议，如发现孩子可能处于某个发展阶段的特殊表现]

请用温暖、专业且易懂的语言，就像一位经验丰富的教育专家在与家长面对面交流一样。分析要深入但不过于学术化，建议要具体可操作。
```

## 实现优先级

### Phase 1: 核心功能
1. DeepSeek R1 API集成
2. 基础的分析报告生成
3. 基础的成长报告生成
4. Markdown报告显示

### Phase 2: 完善功能
1. 使用限制机制
2. 网络状态检测
3. 错误处理优化
4. 历史记录管理

### Phase 3: 优化体验
1. 加载状态优化
2. 报告分享功能
3. 用户体验细节优化

## 技术实现注意事项

1. **数据安全**：确保敏感信息脱敏处理
2. **性能优化**：合理控制API调用频率
3. **用户体验**：提供清晰的加载状态和错误提示
4. **兼容性**：确保iOS 15.6+兼容性
5. **本地化**：支持中文本地化字符串

## 测试策略

1. **单元测试**：API调用、数据处理逻辑
2. **集成测试**：完整的分析流程测试
3. **用户测试**：真实场景下的功能验证
4. **边界测试**：网络异常、数据异常等边界情况
